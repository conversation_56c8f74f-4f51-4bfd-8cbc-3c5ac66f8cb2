[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/CustomDialog.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/CustomDialog.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/CustomMessageBox.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/CustomMessageBox.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/camera_param.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/camera_param.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/camerastream.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/camerastream.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/ch9350_mouse.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/ch9350_mouse.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/main.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/main.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/mainwindow.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/mainwindow.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++", "/home/<USER>/qt_sample/RWS-3576/player.cpp"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/player.cpp"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/CustomDialog.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/CustomDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/CustomMessageBox.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/CustomMessageBox.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/camera_param.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/camera_param.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/camerastream.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/camerastream.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/ch9350_mouse.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/ch9350_mouse.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/mainwindow.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/player.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/player.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/thumbnailprovider.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/thumbnailprovider.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/uart.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/uart.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_player.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_player.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-std=gnu++1z", "-Wall", "-Wextra", "-D_REENTRANT", "-fPIC", "-fsyntax-only", "--target=aarch64-linux-gnu", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/aarch64-linux-gnu", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include/c++/8.3.0/backward", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c++-header", "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_mainwindow.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "/home/<USER>/qt_sample/RWS-3576/uart.c"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/uart.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/CustomDialog.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/CustomDialog.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/CustomMessageBox.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/CustomMessageBox.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/camera_param.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/camera_param.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/camerastream.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/camerastream.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/ch9350_mouse.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/ch9350_mouse.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/mainwindow.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/mainwindow.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/player.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/player.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/thumbnailprovider.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/thumbnailprovider.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/uart.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/uart.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_player.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_player.h"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-pipe", "-D_LARGEFILE_SOURCE", "-D_LARGEFILE64_SOURCE", "-D_FILE_OFFSET_BITS=64", "-g0", "-D_FORTIFY_SOURCE=1", "--sysroot=/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot", "-Wall", "-Wextra", "-D_REENTRANT", "-fsyntax-only", "--target=aarch64-linux-gnu", "-std=gnu17", "-DQT_QML_DEBUG", "-DQT_WIDGETS_LIB", "-DQT_GUI_LIB", "-DQT_CORE_LIB", "-DQ_CREATOR_RUN", "-DQT_ANNOTATE_FUNCTION(x)=__attribute__((annotate(#x)))", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders", "-I/home/<USER>/Qt/Tools/QtCreator/share/qtcreator/cplusplus/wrappedQtHeaders/QtCore", "-I/home/<USER>/qt_sample/RWS-3576", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/gstreamer1-1.22.9/build", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/target/usr/lib/gstreamer-1.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11/QtGui/qpa", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtGui/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5/QtCore/5.15.11/QtCore", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/qt5", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/lib/glib-2.0/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/include/glib-2.0", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtWidgets", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtGui", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/include/QtCore", "-I/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include/libdrm", "-I/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/build/qt5base-da6e958319e95fe564d3b30c931492dd666bfaff/mkspecs/devices/linux-buildroot-g++", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/19/include", "-isystem", "/home/<USER>/opt/gcc-aarch64-linux-gnu-8.3.0/aarch64-linux-gnu/include", "-isystem", "/home/<USER>/RK3576_Linux6.1_SDK_RELEASE_V1.1.0/buildroot/output/rockchip_rk3576/host/aarch64-buildroot-linux-gnu/sysroot/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_mainwindow.h"], "directory": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/.qtc_clangd", "file": "/home/<USER>/qt_sample/RWS-3576/build/rk3576-Debug/ui_mainwindow.h"}]